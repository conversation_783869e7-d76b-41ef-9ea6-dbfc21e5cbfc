package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"chatport-go/internal/monitoring"
)

// MetricsSnapshot represents a snapshot of metrics without mutex
type MetricsSnapshot struct {
	MessagesReceived   int64     `json:"messages_received"`
	MessagesSent       int64     `json:"messages_sent"`
	MessagesFailed     int64     `json:"messages_failed"`
	AIServiceCalls     int64     `json:"ai_service_calls"`
	AIServiceFailures  int64     `json:"ai_service_failures"`
	HTTPRequests       int64     `json:"http_requests"`
	HTTPErrors         int64     `json:"http_errors"`
	WhatsAppReconnects int64     `json:"whatsapp_reconnects"`
	LastMessageTime    time.Time `json:"last_message_time"`
	LastAIServiceCall  time.Time `json:"last_ai_service_call"`
	StartTime          time.Time `json:"start_time"`
}

// MetricsResponse represents the metrics endpoint response
type MetricsResponse struct {
	Metrics   MetricsSnapshot `json:"metrics"`
	Uptime    string          `json:"uptime"`
	Rates     MetricsRates    `json:"rates"`
	Timestamp time.Time       `json:"timestamp"`
}

// MetricsRates represents success rates for various operations
type MetricsRates struct {
	MessageSuccessRate   float64 `json:"message_success_rate"`
	AIServiceSuccessRate float64 `json:"ai_service_success_rate"`
	HTTPSuccessRate      float64 `json:"http_success_rate"`
}

// MetricsHandler handles GET /api/metrics requests
func MetricsHandler(w http.ResponseWriter, r *http.Request) {
	metrics := monitoring.GetMetrics()
	snapshot := metrics.GetSnapshot()

	// Convert to MetricsSnapshot to avoid mutex issues
	metricsSnapshot := MetricsSnapshot{
		MessagesReceived:   snapshot.MessagesReceived,
		MessagesSent:       snapshot.MessagesSent,
		MessagesFailed:     snapshot.MessagesFailed,
		AIServiceCalls:     snapshot.AIServiceCalls,
		AIServiceFailures:  snapshot.AIServiceFailures,
		HTTPRequests:       snapshot.HTTPRequests,
		HTTPErrors:         snapshot.HTTPErrors,
		WhatsAppReconnects: snapshot.WhatsAppReconnects,
		LastMessageTime:    snapshot.LastMessageTime,
		LastAIServiceCall:  snapshot.LastAIServiceCall,
		StartTime:          snapshot.StartTime,
	}

	response := MetricsResponse{
		Metrics: metricsSnapshot,
		Uptime:  metrics.GetUptime().String(),
		Rates: MetricsRates{
			MessageSuccessRate:   metrics.GetMessageSuccessRate(),
			AIServiceSuccessRate: metrics.GetAIServiceSuccessRate(),
			HTTPSuccessRate:      metrics.GetHTTPSuccessRate(),
		},
		Timestamp: time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
