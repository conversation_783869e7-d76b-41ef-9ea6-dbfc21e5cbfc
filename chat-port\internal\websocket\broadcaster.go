package websocket

import (
	"context"
	"sync"
	"time"

	"chatport-go/internal/logger"
	"chatport-go/internal/monitoring"
)

// Broadcaster handles broadcasting messages to WebSocket clients
type Broadcaster struct {
	hub    *Hub
	ctx    context.Context
	cancel context.CancelFunc

	// Channels for different types of broadcasts
	whatsapp<PERSON>han chan *IncomingWhatsAppMessage
	statusChan   chan *StatusUpdate
	metricsChan  chan *MetricsUpdate
	healthChan   chan *HealthUpdate

	// Configuration
	config *BroadcasterConfig

	// Metrics ticker for periodic updates
	metricsTicker *time.Ticker

	// Last known states for change detection
	lastHealthStatus string
	lastMetrics      *MetricsUpdate
	mu               sync.RWMutex
}

// BroadcasterConfig holds broadcaster configuration
type BroadcasterConfig struct {
	MetricsInterval        time.Duration `json:"metrics_interval"`
	BufferSize             int           `json:"buffer_size"`
	EnableMetricsBroadcast bool          `json:"enable_metrics_broadcast"`
	EnableHealthBroadcast  bool          `json:"enable_health_broadcast"`
}

// DefaultBroadcasterConfig returns default broadcaster configuration
func DefaultBroadcasterConfig() *BroadcasterConfig {
	return &BroadcasterConfig{
		MetricsInterval:        10 * time.Second,
		BufferSize:             100,
		EnableMetricsBroadcast: true,
		EnableHealthBroadcast:  true,
	}
}

// NewBroadcaster creates a new broadcaster
func NewBroadcaster(hub *Hub, config *BroadcasterConfig) *Broadcaster {
	if config == nil {
		config = DefaultBroadcasterConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	broadcaster := &Broadcaster{
		hub:           hub,
		ctx:           ctx,
		cancel:        cancel,
		whatsappChan:  make(chan *IncomingWhatsAppMessage, config.BufferSize),
		statusChan:    make(chan *StatusUpdate, config.BufferSize),
		metricsChan:   make(chan *MetricsUpdate, config.BufferSize),
		healthChan:    make(chan *HealthUpdate, config.BufferSize),
		config:        config,
		metricsTicker: time.NewTicker(config.MetricsInterval),
	}

	return broadcaster
}

// Run starts the broadcaster
func (b *Broadcaster) Run() {
	defer b.metricsTicker.Stop()

	logger.Info("WebSocket broadcaster started")

	for {
		select {
		case <-b.ctx.Done():
			logger.Info("WebSocket broadcaster shutting down")
			return

		case msg := <-b.whatsappChan:
			b.broadcastWhatsAppMessage(msg)

		case status := <-b.statusChan:
			b.broadcastStatusUpdate(status)

		case metrics := <-b.metricsChan:
			b.broadcastMetricsUpdate(metrics)

		case health := <-b.healthChan:
			b.broadcastHealthUpdate(health)

		case <-b.metricsTicker.C:
			if b.config.EnableMetricsBroadcast {
				b.broadcastCurrentMetrics()
			}
		}
	}
}

// Stop stops the broadcaster
func (b *Broadcaster) Stop() {
	b.cancel()
}

// BroadcastWhatsAppMessage broadcasts an incoming WhatsApp message
func (b *Broadcaster) BroadcastWhatsAppMessage(from, message, messageID string) {
	msg := &IncomingWhatsAppMessage{
		From:      from,
		Message:   message,
		Timestamp: time.Now(),
		MessageID: messageID,
	}

	select {
	case b.whatsappChan <- msg:
	case <-b.ctx.Done():
	default:
		logger.Warn("WhatsApp message channel is full, dropping message")
	}
}

// BroadcastStatusUpdate broadcasts a service status update
func (b *Broadcaster) BroadcastStatusUpdate(service, status, details string) {
	update := &StatusUpdate{
		Service:   service,
		Status:    status,
		Details:   details,
		Timestamp: time.Now(),
	}

	select {
	case b.statusChan <- update:
	case <-b.ctx.Done():
	default:
		logger.Warn("Status update channel is full, dropping update")
	}
}

// BroadcastMetricsUpdate broadcasts a metrics update
func (b *Broadcaster) BroadcastMetricsUpdate(metrics *MetricsUpdate) {
	select {
	case b.metricsChan <- metrics:
	case <-b.ctx.Done():
	default:
		logger.Warn("Metrics update channel is full, dropping update")
	}
}

// BroadcastHealthUpdate broadcasts a health status update
func (b *Broadcaster) BroadcastHealthUpdate(status string, services map[string]interface{}) {
	b.mu.Lock()
	previousStatus := b.lastHealthStatus
	b.lastHealthStatus = status
	b.mu.Unlock()

	// Only broadcast if status changed or if it's the first update
	if previousStatus != status || previousStatus == "" {
		update := &HealthUpdate{
			Status:         status,
			Services:       services,
			Timestamp:      time.Now(),
			PreviousStatus: previousStatus,
		}

		select {
		case b.healthChan <- update:
		case <-b.ctx.Done():
		default:
			logger.Warn("Health update channel is full, dropping update")
		}
	}
}

// broadcastWhatsAppMessage handles WhatsApp message broadcasting
func (b *Broadcaster) broadcastWhatsAppMessage(msg *IncomingWhatsAppMessage) {
	wsMsg, err := NewIncomingWhatsAppMessage(msg.From, msg.Message, msg.MessageID)
	if err != nil {
		logger.Errorf("Failed to create WhatsApp WebSocket message: %v", err)
		return
	}

	b.hub.BroadcastToSubscription(wsMsg, SubscriptionWhatsApp)
	logger.Debugf("Broadcasted WhatsApp message from %s to WebSocket clients", msg.From)
}

// broadcastStatusUpdate handles status update broadcasting
func (b *Broadcaster) broadcastStatusUpdate(status *StatusUpdate) {
	wsMsg, err := NewStatusUpdate(status.Service, status.Status, status.Details)
	if err != nil {
		logger.Errorf("Failed to create status update WebSocket message: %v", err)
		return
	}

	b.hub.BroadcastToSubscription(wsMsg, SubscriptionStatus)
	logger.Debugf("Broadcasted status update for %s: %s", status.Service, status.Status)
}

// broadcastMetricsUpdate handles metrics update broadcasting
func (b *Broadcaster) broadcastMetricsUpdate(metrics *MetricsUpdate) {
	wsMsg, err := NewMetricsUpdate(*metrics)
	if err != nil {
		logger.Errorf("Failed to create metrics update WebSocket message: %v", err)
		return
	}

	b.hub.BroadcastToSubscription(wsMsg, SubscriptionMetrics)
	logger.Debugf("Broadcasted metrics update to WebSocket clients")
}

// broadcastHealthUpdate handles health update broadcasting
func (b *Broadcaster) broadcastHealthUpdate(health *HealthUpdate) {
	wsMsg, err := NewHealthUpdate(health.Status, health.Services, health.PreviousStatus)
	if err != nil {
		logger.Errorf("Failed to create health update WebSocket message: %v", err)
		return
	}

	b.hub.BroadcastToSubscription(wsMsg, SubscriptionHealth)
	logger.Infof("Broadcasted health update: %s -> %s", health.PreviousStatus, health.Status)
}

// broadcastCurrentMetrics broadcasts current metrics from the monitoring system
func (b *Broadcaster) broadcastCurrentMetrics() {
	metrics := monitoring.GetMetrics()
	snapshot := metrics.GetSnapshot()

	metricsUpdate := &MetricsUpdate{
		MessagesReceived:     snapshot.MessagesReceived,
		MessagesSent:         snapshot.MessagesSent,
		MessagesFailed:       snapshot.MessagesFailed,
		AIServiceCalls:       snapshot.AIServiceCalls,
		AIServiceFailures:    snapshot.AIServiceFailures,
		HTTPRequests:         snapshot.HTTPRequests,
		HTTPErrors:           snapshot.HTTPErrors,
		WhatsAppReconnects:   snapshot.WhatsAppReconnects,
		MessageSuccessRate:   metrics.GetMessageSuccessRate(),
		AIServiceSuccessRate: metrics.GetAIServiceSuccessRate(),
		HTTPSuccessRate:      metrics.GetHTTPSuccessRate(),
		Timestamp:            time.Now(),
	}

	// Only broadcast if metrics have changed significantly
	b.mu.RLock()
	shouldBroadcast := b.lastMetrics == nil || b.metricsChanged(b.lastMetrics, metricsUpdate)
	b.mu.RUnlock()

	if shouldBroadcast {
		b.mu.Lock()
		b.lastMetrics = metricsUpdate
		b.mu.Unlock()

		b.broadcastMetricsUpdate(metricsUpdate)
	}
}

// metricsChanged checks if metrics have changed significantly
func (b *Broadcaster) metricsChanged(old, new *MetricsUpdate) bool {
	// Check if any counter has changed
	return old.MessagesReceived != new.MessagesReceived ||
		old.MessagesSent != new.MessagesSent ||
		old.MessagesFailed != new.MessagesFailed ||
		old.AIServiceCalls != new.AIServiceCalls ||
		old.AIServiceFailures != new.AIServiceFailures ||
		old.HTTPRequests != new.HTTPRequests ||
		old.HTTPErrors != new.HTTPErrors ||
		old.WhatsAppReconnects != new.WhatsAppReconnects
}

// GetStats returns broadcaster statistics
func (b *Broadcaster) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"whatsapp_queue_size": len(b.whatsappChan),
		"status_queue_size":   len(b.statusChan),
		"metrics_queue_size":  len(b.metricsChan),
		"health_queue_size":   len(b.healthChan),
		"config":              b.config,
	}
}

// Global broadcaster instance
var globalBroadcaster *Broadcaster
var broadcasterOnce sync.Once

// InitGlobalBroadcaster initializes the global broadcaster
func InitGlobalBroadcaster(hub *Hub, config *BroadcasterConfig) {
	broadcasterOnce.Do(func() {
		globalBroadcaster = NewBroadcaster(hub, config)
		go globalBroadcaster.Run()
	})
}

// GetGlobalBroadcaster returns the global broadcaster instance
func GetGlobalBroadcaster() *Broadcaster {
	return globalBroadcaster
}

// Convenience functions for global broadcaster

// BroadcastWhatsApp broadcasts a WhatsApp message using the global broadcaster
func BroadcastWhatsApp(from, message, messageID string) {
	if globalBroadcaster != nil {
		globalBroadcaster.BroadcastWhatsAppMessage(from, message, messageID)
	}
}

// BroadcastStatus broadcasts a status update using the global broadcaster
func BroadcastStatus(service, status, details string) {
	if globalBroadcaster != nil {
		globalBroadcaster.BroadcastStatusUpdate(service, status, details)
	}
}

// BroadcastHealth broadcasts a health update using the global broadcaster
func BroadcastHealth(status string, services map[string]interface{}) {
	if globalBroadcaster != nil {
		globalBroadcaster.BroadcastHealthUpdate(status, services)
	}
}
