"""
Database configuration and connection management
"""

from typing import AsyncGenerator

import structlog
from sqlalchemy import text
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import DeclarativeBase

try:
    from .config import get_settings
except ImportError:
    from config import get_settings

logger = structlog.get_logger()

# Global variables for database connection
engine = None
async_session_maker = None


class Base(DeclarativeBase):
    """Base class for all database models"""

    pass


async def init_database() -> None:
    """Initialize database connection and create tables"""
    global engine, async_session_maker

    settings = get_settings()

    # Create async engine
    engine = create_async_engine(
        settings.database_url,
        pool_size=settings.database_pool_size,
        max_overflow=settings.database_max_overflow,
        echo=settings.debug,
        future=True,
    )

    # Create session maker
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    # Test database connection
    try:
        async with engine.begin() as conn:
            # Enable pgvector extension
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            logger.info("pgvector extension enabled")

            # Create tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created/verified")

    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session"""
    if async_session_maker is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")

    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def close_database() -> None:
    """Close database connections"""
    global engine
    if engine:
        await engine.dispose()
        logger.info("Database connections closed")


async def check_database_health() -> dict:
    """Check database health and return status"""
    try:
        if not engine:
            return {"status": "error", "message": "Database not initialized"}

        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()

            if row and row[0] == 1:
                # Check pgvector extension
                result = await conn.execute(
                    text(
                        "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')"
                    )
                )
                vector_enabled = result.fetchone()[0]

                return {
                    "status": "healthy",
                    "connection": "active",
                    "pgvector": "enabled" if vector_enabled else "disabled",
                }
            else:
                return {"status": "error", "message": "Database query failed"}

    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return {"status": "error", "message": str(e)}


async def get_database_stats() -> dict:
    """Get database statistics"""
    try:
        if not engine:
            return {"error": "Database not initialized"}

        async with engine.begin() as conn:
            # Get product count
            result = await conn.execute(text("SELECT COUNT(*) FROM products"))
            product_count = result.fetchone()[0]

            # Get database size
            result = await conn.execute(
                text("SELECT pg_size_pretty(pg_database_size(current_database()))")
            )
            db_size = result.fetchone()[0]

            return {
                "product_count": product_count,
                "database_size": db_size,
                "pool_size": engine.pool.size(),
                "checked_out": engine.pool.checkedout(),
            }

    except Exception as e:
        logger.error("Failed to get database stats", error=str(e))
        return {"error": str(e)}
