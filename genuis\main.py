import os
from google import genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()


def generate_ai_explanation():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise EnvironmentError("Please set the GEMINI_API_KEY environment variable.")
    try:
        client = genai.Client(api_key=api_key)
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=["Explain how AI works in a few words."],
            config=types.GenerateContentConfig(
                system_instruction="You are an AI assistant that helps people find information.",
                thinking_config=types.ThinkingConfig(
                    thinking_budget=0
                ),  # Disables thinking
            ),
        )
        return response.text
    except Exception as e:
        return f"Error generating AI explanation: {e}"


def main():
    print("AI Explanation (Gemini 2.5 Flash):")
    explanation = generate_ai_explanation()
    print(explanation)


if __name__ == "__main__":
    main()
