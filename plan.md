# Detailed Explanation of the Program Architecture

This program is a complete microservice architecture for building a smart WhatsApp bot for a pharmacy using Go and Rust, with true RAG (Retrieval-Augmented Generation) support based on a PostgreSQL database and AI (such as Gemini or local Ollama).

We divide it into three main services:

---

## 📦 1. Go Service (whatsmeow service)

**Function:**

- Acts as the real interface with WhatsApp using the Whatsmeow library.
- Receives messages from customers and displays a QR Code for registration.
- Forwards messages to the Rust service for analysis.
- Receives the final reply and sends it back to the customer on WhatsApp.

**Interface:**

- `POST /api/send` → Sends a message via WhatsApp
- Internal webhook to handle incoming messages

**Tech Stack:**

- Go + Whatsmeow
- REST API for communication with Rust

---

## 🧠 2. Rust Service (Whatsapp AI Service)

**Function:**

- Receives messages from Go.
- Uses RAG to search inside the PostgreSQL database (with pgvector).
- Generates a smart prompt based on search results + the message.
- Sends the prompt to the GPT API.
- Receives the final reply and returns it to the Go service to send to the customer.

**Tech Stack:**

- Rust (Axum framework)
- SeaORM for database interaction
- pgvector extension in PostgreSQL
- GPT API (Gemini or local)

**Internal Logic:**

1. Customer: “Do you have a Concor alternative?”
2. Rust:
   - Analyzes the message
   - Generates an embedding for the message
   - Searches for the most semantically similar products using pgvector
   - Prepares a prompt with the search results + the original message
   - Sends the prompt to GPT
   - Receives the reply and sends it back to the Go service

---

## 🗂️ 3. Data Uploader Tool (Rust)

**Function:**

- Reads from a CSV file containing drug data (name, substance, price, description)
- Generates an embedding for each product using the Gemini API
- Saves the product + embedding in the `products` table in PostgreSQL

**Steps:**

1. Reads CSV → Each row: drug name, substance, price, description
2. Sends the description to Gemini and receives the embedding
3. Inserts all data into the `products` table in PostgreSQL

**File:** `src/bin/embedding_uploader.rs`

**Output:** A database ready for smart querying.

---

## 🧪 Database: PostgreSQL

**Main Table:**

- `products`
  - id
  - name
  - substance
  - price
  - description
  - embedding VECTOR ← pgvector

The `<#>` operator is used to search for semantically similar products.

**Example Query:**

```sql
SELECT * FROM products ORDER BY embedding <#> $1 LIMIT 3;
```

---

## ⚙️ Full Operation Flow (Journey):

1. Customer sends a message on WhatsApp
2. Go service receives the message and sends it to Rust
3. Rust:
   - Generates an embedding for the message
   - Searches the database for semantically similar products
   - Builds a prompt with the appropriate reply
   - Sends the prompt to GPT
   - Receives the reply and sends it to the Go service
4. Go service sends the reply to the customer

---

## 🚀 Current Features:

- Complete separation between the messaging layer and the intelligence layer
- Smart search support even if the customer writes with mistakes or in a different form
- Human-like drug alternatives support
- Scalable database
