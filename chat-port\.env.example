# ChatPort WhatsApp Service Configuration

# =============================================================================
# Server Configuration
# =============================================================================

# HTTP server port
SERVER_PORT=8081

# Server timeouts (Go duration format: 1s, 1m, 1h)
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_IDLE_TIMEOUT=60s

# =============================================================================
# WhatsApp Configuration
# =============================================================================

# WhatsApp session database path
WHATSAPP_DB_PATH=file:session.db?_foreign_keys=on

# QR code scan timeout
WHATSAPP_QR_TIMEOUT=2m

# Reconnection settings
WHATSAPP_RECONNECT_DELAY=5s
WHATSAPP_MAX_RECONNECTS=5

# =============================================================================
# AI Service Configuration
# =============================================================================

# AI service URL (Rust med-intel service)
AI_SERVICE_URL=http://localhost:8080

# AI service timeout
AI_SERVICE_TIMEOUT=30s

# Retry settings for AI service calls
AI_SERVICE_RETRY_ATTEMPTS=3
AI_SERVICE_RETRY_DELAY=1s

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level: debug, info, warn, error
LOG_LEVEL=info

# Log format: text, json
LOG_FORMAT=text
