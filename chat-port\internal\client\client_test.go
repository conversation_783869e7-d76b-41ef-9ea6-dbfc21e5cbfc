package client

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"chatport-go/config"
)

// Mock HTTP server for AI service testing
func createMockAIServer(t *testing.T, response AIResponse, statusCode int) *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request method and content type
		if r.Method != "POST" {
			t.<PERSON>("Expected POST request, got %s", r.Method)
		}

		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("Expected Content-Type application/json, got %s", r.Header.Get("Content-Type"))
		}

		// Verify request body structure
		var payload MessagePayload
		err := json.NewDecoder(r.Body).Decode(&payload)
		if err != nil {
			t.Errorf("Failed to decode request body: %v", err)
		}

		if payload.Number == "" || payload.Message == "" {
			t.Error("Expected number and message in request payload")
		}

		// Send response
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		w.<PERSON>rite<PERSON>ead<PERSON>(statusCode)
		json.NewEncoder(w).Encode(response)
	}))
}

func TestMessagePayload(t *testing.T) {
	payload := MessagePayload{
		Number:  "1234567890",
		Message: "Test message",
	}

	// Test JSON marshaling
	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal MessagePayload: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled MessagePayload
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MessagePayload: %v", err)
	}

	if unmarshaled.Number != payload.Number {
		t.Errorf("Expected number %s, got %s", payload.Number, unmarshaled.Number)
	}

	if unmarshaled.Message != payload.Message {
		t.Errorf("Expected message %s, got %s", payload.Message, unmarshaled.Message)
	}
}

func TestAIResponse(t *testing.T) {
	response := AIResponse{
		Reply: "Test reply",
		Error: "Test error",
	}

	// Test JSON marshaling
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal AIResponse: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled AIResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal AIResponse: %v", err)
	}

	if unmarshaled.Reply != response.Reply {
		t.Errorf("Expected reply %s, got %s", response.Reply, unmarshaled.Reply)
	}

	if unmarshaled.Error != response.Error {
		t.Errorf("Expected error %s, got %s", response.Error, unmarshaled.Error)
	}
}

func TestWhatsAppClient_Structure(t *testing.T) {
	// Test that WhatsAppClient has expected fields
	client := &WhatsAppClient{
		aiService: "http://localhost:8080",
	}

	if client.aiService != "http://localhost:8080" {
		t.Errorf("Expected aiService to be set correctly")
	}
}

func TestInitClient_Configuration(t *testing.T) {
	// Test configuration loading
	originalEnv := map[string]string{
		"WHATSAPP_DB_PATH": os.Getenv("WHATSAPP_DB_PATH"),
		"AI_SERVICE_URL":   os.Getenv("AI_SERVICE_URL"),
	}

	// Clean up after test
	defer func() {
		for key, value := range originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	// Set test environment variables
	os.Setenv("WHATSAPP_DB_PATH", "test:memory:?cache=shared")
	os.Setenv("AI_SERVICE_URL", "http://test-ai-service:8080")

	// Note: We can't fully test InitClient() without mocking the WhatsApp dependencies
	// This test focuses on the configuration aspects that we can verify

	// Test that config.LoadEnv() is called (indirectly)
	config.LoadEnv()

	dbPath := config.GetEnv("WHATSAPP_DB_PATH", "default")
	if dbPath != "test:memory:?cache=shared" {
		t.Errorf("Expected DB path from environment, got %s", dbPath)
	}

	aiURL := config.GetEnv("AI_SERVICE_URL", "default")
	if aiURL != "http://test-ai-service:8080" {
		t.Errorf("Expected AI service URL from environment, got %s", aiURL)
	}
}

func TestForwardToAIService_MockIntegration(t *testing.T) {
	tests := []struct {
		name          string
		aiResponse    AIResponse
		statusCode    int
		expectError   bool
		expectedReply string
	}{
		{
			name: "successful AI response",
			aiResponse: AIResponse{
				Reply: "Hello! How can I help you?",
			},
			statusCode:    http.StatusOK,
			expectError:   false,
			expectedReply: "Hello! How can I help you?",
		},
		{
			name: "AI service error response",
			aiResponse: AIResponse{
				Error: "Service temporarily unavailable",
			},
			statusCode:  http.StatusOK,
			expectError: true,
		},
		{
			name:        "AI service HTTP error",
			aiResponse:  AIResponse{},
			statusCode:  http.StatusInternalServerError,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock AI server
			mockServer := createMockAIServer(t, tt.aiResponse, tt.statusCode)
			defer mockServer.Close()

			// Create client with mock server URL
			_ = &WhatsAppClient{
				aiService: mockServer.URL,
			}

			// We can't easily test the full forwardToAIService method without mocking
			// the WhatsApp client, but we can test the HTTP communication part

			// Test the HTTP request creation and parsing logic
			payload := MessagePayload{
				Number:  "1234567890",
				Message: "Test message",
			}

			// This would be part of the forwardToAIService method
			jsonData, err := json.Marshal(payload)
			if err != nil {
				t.Fatalf("Failed to marshal payload: %v", err)
			}

			// Verify JSON structure
			var testPayload MessagePayload
			err = json.Unmarshal(jsonData, &testPayload)
			if err != nil {
				t.Fatalf("Failed to unmarshal payload: %v", err)
			}

			if testPayload.Number != payload.Number {
				t.Errorf("Expected number %s, got %s", payload.Number, testPayload.Number)
			}
		})
	}
}

func TestClient_ErrorHandling(t *testing.T) {
	// Test various error scenarios that the client should handle

	tests := []struct {
		name        string
		setupClient func() *WhatsAppClient
		expectNil   bool
	}{
		{
			name: "client with valid AI service URL",
			setupClient: func() *WhatsAppClient {
				client := &WhatsAppClient{
					aiService: "http://localhost:8080",
				}
				// Use the client to avoid "declared and not used" error
				_ = client.aiService
				return client
			},
			expectNil: false,
		},
		{
			name: "client with empty AI service URL",
			setupClient: func() *WhatsAppClient {
				client := &WhatsAppClient{
					aiService: "",
				}
				// Use the client to avoid "declared and not used" error
				_ = client.aiService
				return client
			},
			expectNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := tt.setupClient()

			if tt.expectNil && client != nil {
				t.Error("Expected client to be nil")
			}

			if !tt.expectNil && client == nil {
				t.Error("Expected client to not be nil")
			}
		})
	}
}

func TestClient_ConfigurationDefaults(t *testing.T) {
	// Test that configuration defaults are properly applied

	// Clear environment variables
	envVars := []string{
		"WHATSAPP_DB_PATH",
		"AI_SERVICE_URL",
		"WHATSAPP_QR_TIMEOUT",
		"WHATSAPP_RECONNECT_DELAY",
		"WHATSAPP_MAX_RECONNECTS",
	}

	originalValues := make(map[string]string)
	for _, env := range envVars {
		originalValues[env] = os.Getenv(env)
		os.Unsetenv(env)
	}

	// Restore environment after test
	defer func() {
		for env, value := range originalValues {
			if value != "" {
				os.Setenv(env, value)
			}
		}
	}()

	// Test default values
	dbPath := config.GetEnv("WHATSAPP_DB_PATH", "file:session.db?_foreign_keys=on")
	if dbPath != "file:session.db?_foreign_keys=on" {
		t.Errorf("Expected default DB path, got %s", dbPath)
	}

	aiURL := config.GetEnv("AI_SERVICE_URL", "http://localhost:8080")
	if aiURL != "http://localhost:8080" {
		t.Errorf("Expected default AI service URL, got %s", aiURL)
	}
}

func TestClient_MessageValidation(t *testing.T) {
	// Test message validation logic that would be used in the client

	tests := []struct {
		name    string
		number  string
		message string
		valid   bool
	}{
		{
			name:    "valid message",
			number:  "1234567890",
			message: "Hello, world!",
			valid:   true,
		},
		{
			name:    "empty number",
			number:  "",
			message: "Hello, world!",
			valid:   false,
		},
		{
			name:    "empty message",
			number:  "1234567890",
			message: "",
			valid:   false,
		},
		{
			name:    "both empty",
			number:  "",
			message: "",
			valid:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This simulates the validation logic that would be in the client
			valid := tt.number != "" && tt.message != ""

			if valid != tt.valid {
				t.Errorf("Expected validation result %v, got %v", tt.valid, valid)
			}
		})
	}
}

func TestClient_AIServiceCommunication(t *testing.T) {
	// Test the AI service communication protocol

	// Create a mock server that validates the request format
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Validate request
		if r.Method != "POST" {
			t.Errorf("Expected POST method, got %s", r.Method)
		}

		if r.URL.Path != "/api/process" {
			t.Errorf("Expected /api/process path, got %s", r.URL.Path)
		}

		contentType := r.Header.Get("Content-Type")
		if contentType != "application/json" {
			t.Errorf("Expected application/json content type, got %s", contentType)
		}

		// Parse and validate request body
		var payload MessagePayload
		err := json.NewDecoder(r.Body).Decode(&payload)
		if err != nil {
			t.Errorf("Failed to decode request body: %v", err)
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// Send valid response
		response := AIResponse{
			Reply: "Test response for: " + payload.Message,
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// Test the communication protocol
	payload := MessagePayload{
		Number:  "1234567890",
		Message: "Test message",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	// Make request to mock server
	resp, err := http.Post(server.URL+"/api/process", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	// Parse response
	var aiResponse AIResponse
	err = json.NewDecoder(resp.Body).Decode(&aiResponse)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	expectedReply := "Test response for: Test message"
	if aiResponse.Reply != expectedReply {
		t.Errorf("Expected reply %q, got %q", expectedReply, aiResponse.Reply)
	}
}

func TestClient_Timeouts(t *testing.T) {
	// Test timeout handling

	// Create a slow server
	slowServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // Simulate slow response
		w.WriteHeader(http.StatusOK)
	}))
	defer slowServer.Close()

	// Test that we can configure timeouts
	client := &http.Client{
		Timeout: 100 * time.Millisecond, // Short timeout
	}

	_, err := client.Get(slowServer.URL)
	if err == nil {
		t.Error("Expected timeout error, got nil")
	}

	// Check that it's a timeout error
	if !strings.Contains(err.Error(), "timeout") && !strings.Contains(err.Error(), "deadline") {
		t.Errorf("Expected timeout error, got: %v", err)
	}
}
