[workspace]
members = [".", "db/*"]
resolver = "2"

[package]
name = "wellbot-core"
version = "0.1.0"
edition = "2024"

[dependencies]

[workspace.dependencies]
argon2 = "0.5.3"
async-recursion = "1.1.1"
async-trait = "0.1"
chrono = "0.4.40"
config = "0.15.11"
derive_more = "2.0.1"
derive-getters = "0.5"
dotenv = "0.15.0"
jsonwebtoken = "9.3.1"
pretty_assertions = "1.4.1"
rust_decimal = "1.37.1"
sea-orm = "1"
sea-orm-migration = "1"
serde = "1.0"
serde_json = "1.0"
thiserror = "2"
tokio = "1.45.1"
toml = "0.8.8"
tracing = "0.1"
tracing-subscriber = "0.3"
typed-builder = "0.21.0"
url = "2"
uuid = "1.17.0"

db_entity = { path = "db/entity" }
db_migration = { path = "db/migration" }
db_service = { path = "db/service" }
