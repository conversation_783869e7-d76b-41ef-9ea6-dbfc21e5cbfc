package monitoring

import (
	"testing"
	"time"
)

func TestMetrics_Increment(t *testing.T) {
	metrics := &Metrics{
		StartTime: time.Now(),
	}

	// Test increment functions
	metrics.IncrementMessagesReceived()
	if metrics.MessagesReceived != 1 {
		t.<PERSON><PERSON><PERSON>("Expected MessagesReceived to be 1, got %d", metrics.MessagesReceived)
	}

	metrics.IncrementMessagesSent()
	if metrics.MessagesSent != 1 {
		t.<PERSON><PERSON><PERSON>("Expected MessagesSent to be 1, got %d", metrics.MessagesSent)
	}

	metrics.IncrementMessagesFailed()
	if metrics.MessagesFailed != 1 {
		t.<PERSON><PERSON><PERSON>("Expected MessagesFailed to be 1, got %d", metrics.MessagesFailed)
	}

	metrics.IncrementAIServiceCalls()
	if metrics.AIServiceCalls != 1 {
		t.<PERSON><PERSON><PERSON>("Expected AIServiceCalls to be 1, got %d", metrics.AIServiceCalls)
	}

	metrics.IncrementAIServiceFailures()
	if metrics.AIServiceFailures != 1 {
		t.E<PERSON>rf("Expected AIServiceFailures to be 1, got %d", metrics.AIServiceFailures)
	}

	metrics.IncrementHTTPRequests()
	if metrics.HTTPRequests != 1 {
		t.Errorf("Expected HTTPRequests to be 1, got %d", metrics.HTTPRequests)
	}

	metrics.IncrementHTTPErrors()
	if metrics.HTTPErrors != 1 {
		t.Errorf("Expected HTTPErrors to be 1, got %d", metrics.HTTPErrors)
	}

	metrics.IncrementWhatsAppReconnects()
	if metrics.WhatsAppReconnects != 1 {
		t.Errorf("Expected WhatsAppReconnects to be 1, got %d", metrics.WhatsAppReconnects)
	}
}

func TestMetrics_Timestamps(t *testing.T) {
	metrics := &Metrics{
		StartTime: time.Now(),
	}

	// Test that timestamps are updated
	beforeMessage := time.Now()
	metrics.IncrementMessagesReceived()
	afterMessage := time.Now()

	if metrics.LastMessageTime.Before(beforeMessage) || metrics.LastMessageTime.After(afterMessage) {
		t.Error("LastMessageTime should be updated when incrementing messages received")
	}

	beforeAI := time.Now()
	metrics.IncrementAIServiceCalls()
	afterAI := time.Now()

	if metrics.LastAIServiceCall.Before(beforeAI) || metrics.LastAIServiceCall.After(afterAI) {
		t.Error("LastAIServiceCall should be updated when incrementing AI service calls")
	}
}

func TestMetrics_GetSnapshot(t *testing.T) {
	metrics := &Metrics{
		StartTime:         time.Now(),
		MessagesReceived:  5,
		MessagesSent:      4,
		MessagesFailed:    1,
		AIServiceCalls:    3,
		AIServiceFailures: 1,
		HTTPRequests:      10,
		HTTPErrors:        2,
	}

	snapshot := metrics.GetSnapshot()

	// Verify snapshot contains correct values
	if snapshot.MessagesReceived != 5 {
		t.Errorf("Expected MessagesReceived to be 5, got %d", snapshot.MessagesReceived)
	}

	if snapshot.MessagesSent != 4 {
		t.Errorf("Expected MessagesSent to be 4, got %d", snapshot.MessagesSent)
	}

	if snapshot.MessagesFailed != 1 {
		t.Errorf("Expected MessagesFailed to be 1, got %d", snapshot.MessagesFailed)
	}

	// Verify modifying snapshot doesn't affect original
	snapshot.MessagesReceived = 100
	if metrics.MessagesReceived != 5 {
		t.Error("Modifying snapshot should not affect original metrics")
	}
}

func TestMetrics_GetUptime(t *testing.T) {
	startTime := time.Now().Add(-1 * time.Hour)
	metrics := &Metrics{
		StartTime: startTime,
	}

	uptime := metrics.GetUptime()

	// Should be approximately 1 hour (allowing for small timing differences)
	expectedUptime := time.Hour
	if uptime < expectedUptime-time.Second || uptime > expectedUptime+time.Second {
		t.Errorf("Expected uptime around %v, got %v", expectedUptime, uptime)
	}
}

func TestMetrics_GetMessageSuccessRate(t *testing.T) {
	tests := []struct {
		name         string
		sent         int64
		failed       int64
		expectedRate float64
	}{
		{
			name:         "perfect success rate",
			sent:         10,
			failed:       0,
			expectedRate: 100.0,
		},
		{
			name:         "50% success rate",
			sent:         5,
			failed:       5,
			expectedRate: 50.0,
		},
		{
			name:         "no messages",
			sent:         0,
			failed:       0,
			expectedRate: 100.0,
		},
		{
			name:         "all failed",
			sent:         0,
			failed:       10,
			expectedRate: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metrics := &Metrics{
				MessagesSent:   tt.sent,
				MessagesFailed: tt.failed,
			}

			rate := metrics.GetMessageSuccessRate()
			if rate != tt.expectedRate {
				t.Errorf("Expected success rate %.1f%%, got %.1f%%", tt.expectedRate, rate)
			}
		})
	}
}

func TestMetrics_GetAIServiceSuccessRate(t *testing.T) {
	tests := []struct {
		name         string
		calls        int64
		failures     int64
		expectedRate float64
	}{
		{
			name:         "perfect success rate",
			calls:        10,
			failures:     0,
			expectedRate: 100.0,
		},
		{
			name:         "50% success rate",
			calls:        10,
			failures:     5,
			expectedRate: 50.0,
		},
		{
			name:         "no calls",
			calls:        0,
			failures:     0,
			expectedRate: 100.0,
		},
		{
			name:         "all failed",
			calls:        10,
			failures:     10,
			expectedRate: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metrics := &Metrics{
				AIServiceCalls:    tt.calls,
				AIServiceFailures: tt.failures,
			}

			rate := metrics.GetAIServiceSuccessRate()
			if rate != tt.expectedRate {
				t.Errorf("Expected AI service success rate %.1f%%, got %.1f%%", tt.expectedRate, rate)
			}
		})
	}
}

func TestMetrics_GetHTTPSuccessRate(t *testing.T) {
	tests := []struct {
		name         string
		requests     int64
		errors       int64
		expectedRate float64
	}{
		{
			name:         "perfect success rate",
			requests:     10,
			errors:       0,
			expectedRate: 100.0,
		},
		{
			name:         "80% success rate",
			requests:     10,
			errors:       2,
			expectedRate: 80.0,
		},
		{
			name:         "no requests",
			requests:     0,
			errors:       0,
			expectedRate: 100.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metrics := &Metrics{
				HTTPRequests: tt.requests,
				HTTPErrors:   tt.errors,
			}

			rate := metrics.GetHTTPSuccessRate()
			if rate != tt.expectedRate {
				t.Errorf("Expected HTTP success rate %.1f%%, got %.1f%%", tt.expectedRate, rate)
			}
		})
	}
}

func TestMetrics_Reset(t *testing.T) {
	metrics := &Metrics{
		MessagesReceived:   10,
		MessagesSent:       8,
		MessagesFailed:     2,
		AIServiceCalls:     5,
		AIServiceFailures:  1,
		HTTPRequests:       20,
		HTTPErrors:         3,
		WhatsAppReconnects: 2,
		LastMessageTime:    time.Now(),
		LastAIServiceCall:  time.Now(),
		StartTime:          time.Now().Add(-1 * time.Hour),
	}

	beforeReset := time.Now()
	metrics.Reset()
	afterReset := time.Now()

	// Check all counters are reset
	if metrics.MessagesReceived != 0 {
		t.Errorf("Expected MessagesReceived to be 0 after reset, got %d", metrics.MessagesReceived)
	}

	if metrics.MessagesSent != 0 {
		t.Errorf("Expected MessagesSent to be 0 after reset, got %d", metrics.MessagesSent)
	}

	// Check timestamps are reset
	if !metrics.LastMessageTime.IsZero() {
		t.Error("Expected LastMessageTime to be zero after reset")
	}

	if !metrics.LastAIServiceCall.IsZero() {
		t.Error("Expected LastAIServiceCall to be zero after reset")
	}

	// Check StartTime is updated
	if metrics.StartTime.Before(beforeReset) || metrics.StartTime.After(afterReset) {
		t.Error("Expected StartTime to be updated after reset")
	}
}

func TestGetMetrics_Singleton(t *testing.T) {
	// Test that GetMetrics returns the same instance
	metrics1 := GetMetrics()
	metrics2 := GetMetrics()

	if metrics1 != metrics2 {
		t.Error("GetMetrics should return the same instance (singleton pattern)")
	}
}

func TestGlobalFunctions(t *testing.T) {
	// Reset metrics to start fresh
	GetMetrics().Reset()

	// Test global convenience functions
	IncrementMessagesReceived()
	IncrementMessagesSent()
	IncrementMessagesFailed()
	IncrementAIServiceCalls()
	IncrementAIServiceFailures()
	IncrementHTTPRequests()
	IncrementHTTPErrors()
	IncrementWhatsAppReconnects()

	metrics := GetMetrics()
	snapshot := metrics.GetSnapshot()

	// Verify all counters were incremented
	if snapshot.MessagesReceived != 1 {
		t.Errorf("Expected MessagesReceived to be 1, got %d", snapshot.MessagesReceived)
	}

	if snapshot.MessagesSent != 1 {
		t.Errorf("Expected MessagesSent to be 1, got %d", snapshot.MessagesSent)
	}

	if snapshot.MessagesFailed != 1 {
		t.Errorf("Expected MessagesFailed to be 1, got %d", snapshot.MessagesFailed)
	}

	if snapshot.AIServiceCalls != 1 {
		t.Errorf("Expected AIServiceCalls to be 1, got %d", snapshot.AIServiceCalls)
	}

	if snapshot.AIServiceFailures != 1 {
		t.Errorf("Expected AIServiceFailures to be 1, got %d", snapshot.AIServiceFailures)
	}

	if snapshot.HTTPRequests != 1 {
		t.Errorf("Expected HTTPRequests to be 1, got %d", snapshot.HTTPRequests)
	}

	if snapshot.HTTPErrors != 1 {
		t.Errorf("Expected HTTPErrors to be 1, got %d", snapshot.HTTPErrors)
	}

	if snapshot.WhatsAppReconnects != 1 {
		t.Errorf("Expected WhatsAppReconnects to be 1, got %d", snapshot.WhatsAppReconnects)
	}
}
