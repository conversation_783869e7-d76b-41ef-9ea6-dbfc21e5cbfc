package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"chatport-go/internal/monitoring"
)

func TestMetricsHandler(t *testing.T) {
	// Reset metrics for predictable test
	monitoring.GetMetrics().Reset()

	// Add some test data
	monitoring.IncrementMessagesReceived()
	monitoring.IncrementMessagesSent()
	monitoring.IncrementAIServiceCalls()
	monitoring.IncrementHTTPRequests()

	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	// Check status code
	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}

	// Check content type
	contentType := rr.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type application/json, got %s", contentType)
	}

	// Parse response
	var response MetricsResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check metrics values
	if response.Metrics.MessagesReceived != 1 {
		t.Errorf("Expected MessagesReceived to be 1, got %d", response.Metrics.MessagesReceived)
	}

	if response.Metrics.MessagesSent != 1 {
		t.Errorf("Expected MessagesSent to be 1, got %d", response.Metrics.MessagesSent)
	}

	if response.Metrics.AIServiceCalls != 1 {
		t.Errorf("Expected AIServiceCalls to be 1, got %d", response.Metrics.AIServiceCalls)
	}

	if response.Metrics.HTTPRequests != 1 {
		t.Errorf("Expected HTTPRequests to be 1, got %d", response.Metrics.HTTPRequests)
	}

	// Check uptime is present
	if response.Uptime == "" {
		t.Error("Expected uptime to be present")
	}

	// Check timestamp is recent
	if time.Since(response.Timestamp) > time.Minute {
		t.Error("Expected timestamp to be recent")
	}
}

func TestMetricsHandler_SuccessRates(t *testing.T) {
	// Reset metrics for predictable test
	monitoring.GetMetrics().Reset()

	// Add test data with some failures
	for i := 0; i < 10; i++ {
		monitoring.IncrementMessagesSent()
		monitoring.IncrementAIServiceCalls()
		monitoring.IncrementHTTPRequests()
	}

	// Add some failures
	for i := 0; i < 2; i++ {
		monitoring.IncrementMessagesFailed()
		monitoring.IncrementAIServiceFailures()
		monitoring.IncrementHTTPErrors()
	}

	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	var response MetricsResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check success rates
	_ = (10.0 / 12.0) * 100.0 // 10 sent, 2 failed = 83.33%
	if response.Rates.MessageSuccessRate < 83.0 || response.Rates.MessageSuccessRate > 84.0 {
		t.Errorf("Expected message success rate around 83.33%%, got %.2f%%", response.Rates.MessageSuccessRate)
	}

	expectedAIRate := (8.0 / 10.0) * 100.0 // 10 calls, 2 failures = 80%
	if response.Rates.AIServiceSuccessRate != expectedAIRate {
		t.Errorf("Expected AI service success rate %.2f%%, got %.2f%%", expectedAIRate, response.Rates.AIServiceSuccessRate)
	}

	_ = (10.0 / 12.0) * 100.0 // 10 requests, 2 errors = 83.33%
	// Note: HTTP requests include the test request itself, so we need to account for that
	// The actual calculation might be different due to the test request
	if response.Rates.HTTPSuccessRate < 80.0 || response.Rates.HTTPSuccessRate > 85.0 {
		t.Errorf("Expected HTTP success rate around 80-85%%, got %.2f%%", response.Rates.HTTPSuccessRate)
	}
}

func TestMetricsHandler_EmptyMetrics(t *testing.T) {
	// Reset metrics to empty state
	monitoring.GetMetrics().Reset()

	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	var response MetricsResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check all metrics are zero
	if response.Metrics.MessagesReceived != 0 {
		t.Errorf("Expected MessagesReceived to be 0, got %d", response.Metrics.MessagesReceived)
	}

	if response.Metrics.MessagesSent != 0 {
		t.Errorf("Expected MessagesSent to be 0, got %d", response.Metrics.MessagesSent)
	}

	if response.Metrics.MessagesFailed != 0 {
		t.Errorf("Expected MessagesFailed to be 0, got %d", response.Metrics.MessagesFailed)
	}

	// Success rates should be 100% when no operations have occurred
	if response.Rates.MessageSuccessRate != 100.0 {
		t.Errorf("Expected message success rate 100%% with no operations, got %.2f%%", response.Rates.MessageSuccessRate)
	}

	if response.Rates.AIServiceSuccessRate != 100.0 {
		t.Errorf("Expected AI service success rate 100%% with no operations, got %.2f%%", response.Rates.AIServiceSuccessRate)
	}

	if response.Rates.HTTPSuccessRate != 100.0 {
		t.Errorf("Expected HTTP success rate 100%% with no operations, got %.2f%%", response.Rates.HTTPSuccessRate)
	}
}

func TestMetricsHandler_JSONStructure(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	// Verify response is valid JSON
	var result map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&result)
	if err != nil {
		t.Fatalf("Response is not valid JSON: %v", err)
	}

	// Check expected top-level keys
	expectedKeys := []string{"metrics", "uptime", "rates", "timestamp"}
	for _, key := range expectedKeys {
		if _, exists := result[key]; !exists {
			t.Errorf("Expected key %s not found in response", key)
		}
	}

	// Check metrics structure
	if metrics, ok := result["metrics"].(map[string]interface{}); ok {
		metricsKeys := []string{
			"messages_received", "messages_sent", "messages_failed",
			"ai_service_calls", "ai_service_failures",
			"http_requests", "http_errors", "whatsapp_reconnects",
			"last_message_time", "last_ai_service_call", "start_time",
		}
		for _, key := range metricsKeys {
			if _, exists := metrics[key]; !exists {
				t.Errorf("Expected metrics key %s not found", key)
			}
		}
	} else {
		t.Error("Expected metrics to be an object")
	}

	// Check rates structure
	if rates, ok := result["rates"].(map[string]interface{}); ok {
		ratesKeys := []string{"message_success_rate", "ai_service_success_rate", "http_success_rate"}
		for _, key := range ratesKeys {
			if _, exists := rates[key]; !exists {
				t.Errorf("Expected rates key %s not found", key)
			}
		}
	} else {
		t.Error("Expected rates to be an object")
	}
}

func TestMetricsHandler_Timestamps(t *testing.T) {
	// Reset metrics and add some activity
	monitoring.GetMetrics().Reset()
	monitoring.IncrementMessagesReceived()
	monitoring.IncrementAIServiceCalls()

	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	var response MetricsResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check that timestamps are recent
	now := time.Now()

	if response.Metrics.LastMessageTime.IsZero() {
		t.Error("Expected LastMessageTime to be set after incrementing messages")
	}

	if time.Since(response.Metrics.LastMessageTime) > time.Minute {
		t.Error("Expected LastMessageTime to be recent")
	}

	if response.Metrics.LastAIServiceCall.IsZero() {
		t.Error("Expected LastAIServiceCall to be set after incrementing AI service calls")
	}

	if time.Since(response.Metrics.LastAIServiceCall) > time.Minute {
		t.Error("Expected LastAIServiceCall to be recent")
	}

	if response.Metrics.StartTime.After(now) {
		t.Error("Expected StartTime to be in the past")
	}

	if response.Timestamp.Before(now.Add(-time.Minute)) || response.Timestamp.After(now.Add(time.Minute)) {
		t.Error("Expected response timestamp to be recent")
	}
}

func TestMetricsHandler_HTTPMethod(t *testing.T) {
	// Test that handler works with GET method
	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	if rr.Code == http.StatusMethodNotAllowed {
		t.Error("Handler should accept GET method")
	}

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}
}

func TestMetricsHandler_NoBody(t *testing.T) {
	// Test that handler works without request body
	req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
	rr := httptest.NewRecorder()

	MetricsHandler(rr, req)

	// Should not fail
	if rr.Code >= 500 {
		t.Errorf("Handler should not fail with 5xx error, got status %d", rr.Code)
	}

	// Should return valid JSON
	var response MetricsResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Errorf("Response should be valid JSON: %v", err)
	}
}

func TestMetricsSnapshot_JSONMarshaling(t *testing.T) {
	snapshot := MetricsSnapshot{
		MessagesReceived:   10,
		MessagesSent:       8,
		MessagesFailed:     2,
		AIServiceCalls:     5,
		AIServiceFailures:  1,
		HTTPRequests:       20,
		HTTPErrors:         3,
		WhatsAppReconnects: 1,
		LastMessageTime:    time.Now(),
		LastAIServiceCall:  time.Now(),
		StartTime:          time.Now().Add(-time.Hour),
	}

	// Test marshaling
	data, err := json.Marshal(snapshot)
	if err != nil {
		t.Fatalf("Failed to marshal MetricsSnapshot: %v", err)
	}

	// Test unmarshaling
	var unmarshaled MetricsSnapshot
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MetricsSnapshot: %v", err)
	}

	// Check values
	if unmarshaled.MessagesReceived != snapshot.MessagesReceived {
		t.Errorf("Expected MessagesReceived %d, got %d", snapshot.MessagesReceived, unmarshaled.MessagesReceived)
	}

	if unmarshaled.MessagesSent != snapshot.MessagesSent {
		t.Errorf("Expected MessagesSent %d, got %d", snapshot.MessagesSent, unmarshaled.MessagesSent)
	}
}

func TestMetricsRates_JSONMarshaling(t *testing.T) {
	rates := MetricsRates{
		MessageSuccessRate:   95.5,
		AIServiceSuccessRate: 98.2,
		HTTPSuccessRate:      99.1,
	}

	// Test marshaling
	data, err := json.Marshal(rates)
	if err != nil {
		t.Fatalf("Failed to marshal MetricsRates: %v", err)
	}

	// Test unmarshaling
	var unmarshaled MetricsRates
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MetricsRates: %v", err)
	}

	// Check values
	if unmarshaled.MessageSuccessRate != rates.MessageSuccessRate {
		t.Errorf("Expected MessageSuccessRate %.2f, got %.2f", rates.MessageSuccessRate, unmarshaled.MessageSuccessRate)
	}

	if unmarshaled.AIServiceSuccessRate != rates.AIServiceSuccessRate {
		t.Errorf("Expected AIServiceSuccessRate %.2f, got %.2f", rates.AIServiceSuccessRate, unmarshaled.AIServiceSuccessRate)
	}

	if unmarshaled.HTTPSuccessRate != rates.HTTPSuccessRate {
		t.Errorf("Expected HTTPSuccessRate %.2f, got %.2f", rates.HTTPSuccessRate, unmarshaled.HTTPSuccessRate)
	}
}

func TestMetricsHandler_ConcurrentAccess(t *testing.T) {
	// Test that metrics handler can handle concurrent access
	const numRequests = 10

	// Reset metrics
	monitoring.GetMetrics().Reset()

	// Add some metrics in background
	go func() {
		for i := 0; i < 100; i++ {
			monitoring.IncrementHTTPRequests()
			time.Sleep(time.Millisecond)
		}
	}()

	// Make concurrent requests to metrics endpoint
	results := make([]int, numRequests)
	for i := range numRequests {
		go func(index int) {
			req := httptest.NewRequest(http.MethodGet, "/api/metrics", nil)
			rr := httptest.NewRecorder()

			MetricsHandler(rr, req)
			results[index] = rr.Code
		}(i)
	}

	// Wait a bit for requests to complete
	time.Sleep(100 * time.Millisecond)

	// Check that all requests succeeded
	for i, code := range results {
		if code != 0 && code != http.StatusOK {
			t.Errorf("Request %d failed with status %d", i, code)
		}
	}
}
